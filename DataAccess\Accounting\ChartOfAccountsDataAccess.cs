using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات دليل الحسابات
    /// </summary>
    public static class ChartOfAccountsDataAccess
    {
        /// <summary>
        /// الحصول على جميع أنواع الحسابات
        /// </summary>
        /// <returns>قائمة أنواع الحسابات</returns>
        public static List<AccountType> GetAllAccountTypes()
        {
            var accountTypes = new List<AccountType>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT AccountTypeId, AccountTypeCode, AccountTypeName, AccountTypeNameAr, 
                               Description, DisplayOrder, IsActive, CreatedDate
                        FROM AccountTypes 
                        WHERE IsActive = 1
                        ORDER BY DisplayOrder, AccountTypeNameAr";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                accountTypes.Add(new AccountType
                                {
                                    AccountTypeId = Convert.ToInt32(reader["AccountTypeId"]),
                                    AccountTypeCode = reader["AccountTypeCode"].ToString(),
                                    AccountTypeName = reader["AccountTypeName"].ToString(),
                                    AccountTypeNameAr = reader["AccountTypeNameAr"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    DisplayOrder = Convert.ToInt32(reader["DisplayOrder"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب أنواع الحسابات: {ex.Message}");
            }

            return accountTypes;
        }

        /// <summary>
        /// الحصول على جميع مجموعات الحسابات
        /// </summary>
        /// <returns>قائمة مجموعات الحسابات</returns>
        public static List<AccountGroup> GetAllAccountGroups()
        {
            var accountGroups = new List<AccountGroup>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT ag.AccountGroupId, ag.AccountTypeId, ag.GroupCode, ag.GroupName, 
                               ag.GroupNameAr, ag.Description, ag.DisplayOrder, ag.IsActive, 
                               ag.CreatedDate, at.AccountTypeName, at.AccountTypeNameAr
                        FROM AccountGroups ag
                        INNER JOIN AccountTypes at ON ag.AccountTypeId = at.AccountTypeId
                        WHERE ag.IsActive = 1
                        ORDER BY at.DisplayOrder, ag.DisplayOrder, ag.GroupNameAr";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                accountGroups.Add(new AccountGroup
                                {
                                    AccountGroupId = Convert.ToInt32(reader["AccountGroupId"]),
                                    AccountTypeId = Convert.ToInt32(reader["AccountTypeId"]),
                                    GroupCode = reader["GroupCode"].ToString(),
                                    GroupName = reader["GroupName"].ToString(),
                                    GroupNameAr = reader["GroupNameAr"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    DisplayOrder = Convert.ToInt32(reader["DisplayOrder"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    AccountTypeName = reader["AccountTypeName"].ToString(),
                                    AccountTypeNameAr = reader["AccountTypeNameAr"].ToString()
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب مجموعات الحسابات: {ex.Message}");
            }

            return accountGroups;
        }

        /// <summary>
        /// الحصول على جميع الحسابات في الدليل المحاسبي
        /// </summary>
        /// <returns>قائمة الحسابات</returns>
        public static List<ChartOfAccount> GetAllAccounts()
        {
            var accounts = new List<ChartOfAccount>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();

                    // تشخيص: التحقق من الاتصال
                    System.Diagnostics.Debug.WriteLine($"Database connection opened successfully. Database: {connection.Database}");
                    string query = @"
                        SELECT coa.AccountId, coa.AccountCode, coa.AccountName, coa.AccountNameAr,
                               coa.AccountTypeId, coa.AccountGroupId, coa.ParentAccountId, 
                               coa.AccountLevel, coa.IsParent, coa.IsActive, coa.AllowPosting,
                               coa.Description, coa.CurrencyCode, coa.OpeningBalance, 
                               coa.CurrentBalance, coa.CreatedDate,
                               at.AccountTypeName, at.AccountTypeNameAr,
                               ag.GroupName, ag.GroupNameAr,
                               parent.AccountName as ParentAccountName, 
                               parent.AccountNameAr as ParentAccountNameAr
                        FROM ChartOfAccounts coa
                        INNER JOIN AccountTypes at ON coa.AccountTypeId = at.AccountTypeId
                        LEFT JOIN AccountGroups ag ON coa.AccountGroupId = ag.AccountGroupId
                        LEFT JOIN ChartOfAccounts parent ON coa.ParentAccountId = parent.AccountId
                        WHERE coa.IsActive = 1
                        ORDER BY coa.AccountCode";

                    using (var command = new SqlCommand(query, connection))
                    {
                        // تشخيص: عرض الاستعلام
                        System.Diagnostics.Debug.WriteLine($"Executing query: {query}");

                        using (var reader = command.ExecuteReader())
                        {
                            int recordCount = 0;
                            while (reader.Read())
                            {
                                recordCount++;
                                var account = new ChartOfAccount
                                {
                                    AccountId = Convert.ToInt32(reader["AccountId"]),
                                    AccountCode = reader["AccountCode"].ToString(),
                                    AccountName = reader["AccountName"].ToString(),
                                    AccountNameAr = reader["AccountNameAr"].ToString(),
                                    AccountTypeId = Convert.ToInt32(reader["AccountTypeId"]),
                                    AccountGroupId = reader["AccountGroupId"] == DBNull.Value ? 0 : Convert.ToInt32(reader["AccountGroupId"]),
                                    ParentAccountId = reader["ParentAccountId"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["ParentAccountId"]),
                                    AccountLevel = Convert.ToInt32(reader["AccountLevel"]),
                                    IsParent = Convert.ToBoolean(reader["IsParent"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    AllowPosting = Convert.ToBoolean(reader["AllowPosting"]),
                                    Description = reader["Description"].ToString(),
                                    CurrencyCode = reader["CurrencyCode"].ToString(),
                                    OpeningBalance = Convert.ToDecimal(reader["OpeningBalance"]),
                                    CurrentBalance = Convert.ToDecimal(reader["CurrentBalance"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    AccountTypeName = reader["AccountTypeName"].ToString(),
                                    AccountTypeNameAr = reader["AccountTypeNameAr"].ToString(),
                                    GroupName = reader["GroupName"].ToString(),
                                    GroupNameAr = reader["GroupNameAr"].ToString(),
                                    ParentAccountName = reader["ParentAccountName"].ToString(),
                                    ParentAccountNameAr = reader["ParentAccountNameAr"].ToString()
                                };

                                // تحويل AccountLevel إلى LevelType
                                account.LevelType = account.AccountLevel switch
                                {
                                    1 => AccountLevelType.Main,
                                    2 => AccountLevelType.Sub,
                                    3 => AccountLevelType.Detail,
                                    _ => AccountLevelType.Detail
                                };

                                accounts.Add(account);

                                // تشخيص: عرض أول 3 حسابات
                                if (recordCount <= 3)
                                {
                                    System.Diagnostics.Debug.WriteLine($"Account {recordCount}: {account.AccountCode} - {account.AccountNameAr}");
                                }
                            }

                            // تشخيص: عرض العدد الإجمالي
                            System.Diagnostics.Debug.WriteLine($"Total accounts loaded: {recordCount}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetAllAccounts: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw new Exception($"خطأ في جلب دليل الحسابات: {ex.Message}");
            }

            System.Diagnostics.Debug.WriteLine($"Returning {accounts.Count} accounts from GetAllAccounts");
            return accounts;
        }

        /// <summary>
        /// الحصول على الحسابات الرئيسية (المستوى الأول)
        /// </summary>
        /// <returns>قائمة الحسابات الرئيسية</returns>
        public static List<ChartOfAccount> GetMainAccounts()
        {
            var accounts = new List<ChartOfAccount>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT coa.AccountId, coa.AccountCode, coa.AccountName, coa.AccountNameAr,
                               coa.AccountTypeId, coa.AccountGroupId, coa.AccountLevel, 
                               coa.IsParent, coa.IsActive, coa.AllowPosting, coa.Description,
                               coa.CurrencyCode, coa.OpeningBalance, coa.CurrentBalance, 
                               coa.CreatedDate, at.AccountTypeName, at.AccountTypeNameAr,
                               ag.GroupName, ag.GroupNameAr
                        FROM ChartOfAccounts coa
                        INNER JOIN AccountTypes at ON coa.AccountTypeId = at.AccountTypeId
                        LEFT JOIN AccountGroups ag ON coa.AccountGroupId = ag.AccountGroupId
                        WHERE coa.IsActive = 1 AND coa.ParentAccountId IS NULL
                        ORDER BY coa.AccountCode";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                accounts.Add(new ChartOfAccount
                                {
                                    AccountId = Convert.ToInt32(reader["AccountId"]),
                                    AccountCode = reader["AccountCode"].ToString(),
                                    AccountName = reader["AccountName"].ToString(),
                                    AccountNameAr = reader["AccountNameAr"].ToString(),
                                    AccountTypeId = Convert.ToInt32(reader["AccountTypeId"]),
                                    AccountGroupId = reader["AccountGroupId"] == DBNull.Value ? 0 : Convert.ToInt32(reader["AccountGroupId"]),
                                    AccountLevel = Convert.ToInt32(reader["AccountLevel"]),
                                    IsParent = Convert.ToBoolean(reader["IsParent"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    AllowPosting = Convert.ToBoolean(reader["AllowPosting"]),
                                    Description = reader["Description"].ToString(),
                                    CurrencyCode = reader["CurrencyCode"].ToString(),
                                    OpeningBalance = Convert.ToDecimal(reader["OpeningBalance"]),
                                    CurrentBalance = Convert.ToDecimal(reader["CurrentBalance"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    AccountTypeName = reader["AccountTypeName"].ToString(),
                                    AccountTypeNameAr = reader["AccountTypeNameAr"].ToString(),
                                    GroupName = reader["GroupName"].ToString(),
                                    GroupNameAr = reader["GroupNameAr"].ToString()
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الحسابات الرئيسية: {ex.Message}");
            }

            return accounts;
        }

        /// <summary>
        /// إضافة حساب جديد
        /// </summary>
        /// <param name="account">بيانات الحساب</param>
        /// <returns>معرف الحساب الجديد</returns>
        public static int AddAccount(ChartOfAccount account)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO ChartOfAccounts
                        (AccountCode, AccountNameAr, AccountNameEn, ParentAccountId, LevelType, Nature,
                         AccountGroupId, Status, Currency, Description, AllowPosting, AllowDirectEntry,
                         OpeningBalance, OpeningBalanceNature, AccountLevel, IsParent, CreatedDate)
                        VALUES
                        (@AccountCode, @AccountNameAr, @AccountNameEn, @ParentAccountId, @LevelType, @Nature,
                         @AccountGroupId, @Status, @Currency, @Description, @AllowPosting, @AllowDirectEntry,
                         @OpeningBalance, @OpeningBalanceNature, @AccountLevel, @IsParent, @CreatedDate);
                        SELECT SCOPE_IDENTITY();";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                        command.Parameters.AddWithValue("@AccountNameAr", account.AccountNameAr);
                        command.Parameters.AddWithValue("@AccountNameEn", account.AccountNameEn ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ParentAccountId", account.ParentAccountId ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@LevelType", (int)account.LevelType);
                        command.Parameters.AddWithValue("@Nature", (int)account.Nature);
                        command.Parameters.AddWithValue("@AccountGroupId", account.AccountGroupId == 0 ? (object)DBNull.Value : account.AccountGroupId);
                        command.Parameters.AddWithValue("@Status", (int)account.Status);
                        command.Parameters.AddWithValue("@Currency", (int)account.Currency);
                        command.Parameters.AddWithValue("@Description", account.Description ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@AllowPosting", account.AllowPosting);
                        command.Parameters.AddWithValue("@AllowDirectEntry", account.AllowDirectEntry);
                        command.Parameters.AddWithValue("@OpeningBalance", account.OpeningBalance);
                        command.Parameters.AddWithValue("@OpeningBalanceNature", (int)account.OpeningBalanceNature);
                        command.Parameters.AddWithValue("@AccountLevel", account.AccountLevel);
                        command.Parameters.AddWithValue("@IsParent", account.IsParent);
                        command.Parameters.AddWithValue("@CreatedDate", account.CreatedDate);

                        var result = command.ExecuteScalar();
                        return Convert.ToInt32(result);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث بيانات حساب موجود
        /// </summary>
        /// <param name="account">بيانات الحساب المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateAccount(ChartOfAccount account)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        UPDATE ChartOfAccounts SET
                            AccountCode = @AccountCode,
                            AccountNameAr = @AccountNameAr,
                            AccountNameEn = @AccountNameEn,
                            ParentAccountId = @ParentAccountId,
                            LevelType = @LevelType,
                            Nature = @Nature,
                            AccountGroupId = @AccountGroupId,
                            Status = @Status,
                            Currency = @Currency,
                            Description = @Description,
                            AllowPosting = @AllowPosting,
                            AllowDirectEntry = @AllowDirectEntry,
                            OpeningBalance = @OpeningBalance,
                            OpeningBalanceNature = @OpeningBalanceNature,
                            AccountLevel = @AccountLevel,
                            IsParent = @IsParent,
                            ModifiedDate = @ModifiedDate
                        WHERE AccountId = @AccountId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@AccountId", account.AccountId);
                        command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                        command.Parameters.AddWithValue("@AccountNameAr", account.AccountNameAr);
                        command.Parameters.AddWithValue("@AccountNameEn", account.AccountNameEn ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ParentAccountId", account.ParentAccountId ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@LevelType", (int)account.LevelType);
                        command.Parameters.AddWithValue("@Nature", (int)account.Nature);
                        command.Parameters.AddWithValue("@AccountGroupId", account.AccountGroupId == 0 ? (object)DBNull.Value : account.AccountGroupId);
                        command.Parameters.AddWithValue("@Status", (int)account.Status);
                        command.Parameters.AddWithValue("@Currency", (int)account.Currency);
                        command.Parameters.AddWithValue("@Description", account.Description ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@AllowPosting", account.AllowPosting);
                        command.Parameters.AddWithValue("@AllowDirectEntry", account.AllowDirectEntry);
                        command.Parameters.AddWithValue("@OpeningBalance", account.OpeningBalance);
                        command.Parameters.AddWithValue("@OpeningBalanceNature", (int)account.OpeningBalanceNature);
                        command.Parameters.AddWithValue("@AccountLevel", account.AccountLevel);
                        command.Parameters.AddWithValue("@IsParent", account.IsParent);
                        command.Parameters.AddWithValue("@ModifiedDate", account.ModifiedDate ?? DateTime.Now);

                        int rowsAffected = command.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الحساب: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف حساب من الدليل المحاسبي
        /// </summary>
        /// <param name="accountId">معرف الحساب</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteAccount(int accountId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();

                    // التحقق من وجود حسابات فرعية
                    string checkChildrenQuery = @"
                        SELECT COUNT(*)
                        FROM ChartOfAccounts
                        WHERE ParentAccountId = @AccountId AND IsActive = 1";

                    using (var checkCommand = new SqlCommand(checkChildrenQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@AccountId", accountId);
                        int childCount = (int)checkCommand.ExecuteScalar();

                        if (childCount > 0)
                        {
                            throw new Exception("لا يمكن حذف الحساب لأنه يحتوي على حسابات فرعية");
                        }
                    }

                    // حذف الحساب (soft delete)
                    string deleteQuery = @"
                        UPDATE ChartOfAccounts
                        SET IsActive = 0, ModifiedDate = @ModifiedDate
                        WHERE AccountId = @AccountId";

                    using (var command = new SqlCommand(deleteQuery, connection))
                    {
                        command.Parameters.AddWithValue("@AccountId", accountId);
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                        int rowsAffected = command.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الحساب: {ex.Message}", ex);
            }
        }


    }
}
