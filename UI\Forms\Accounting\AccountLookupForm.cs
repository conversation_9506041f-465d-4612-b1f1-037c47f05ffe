using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Services.Accounting;
using UIHelper = Awqaf_Managment.UI.Helpers.UIHelper;

namespace AwqafManagement.UI.Forms.Accounting
{
    public partial class AccountLookupForm : Form
    {
        #region Properties
        
        /// <summary>
        /// الحساب المحدد
        /// </summary>
        public ChartOfAccount SelectedAccount { get; private set; }
        
        /// <summary>
        /// قائمة الحسابات
        /// </summary>
        private List<ChartOfAccount> _accounts;
        
        /// <summary>
        /// قائمة الحسابات المفلترة
        /// </summary>
        private List<ChartOfAccount> _filteredAccounts;
        
        #endregion

        #region Constructor
        
        public AccountLookupForm()
        {
            InitializeComponent();
            InitializeForm();
            LoadAccounts();
        }
        
        #endregion

        #region Form Initialization
        
        /// <summary>
        /// تهيئة النموذج
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // تطبيق الخط العربي
                UIHelper.ApplyArabicFont(this);
                
                // إعداد النموذج
                this.Text = "البحث عن حساب";
                this.StartPosition = FormStartPosition.CenterParent;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;
                this.ShowInTaskbar = false;
                this.KeyPreview = true;
                
                // إعداد DataGridView
                SetupDataGridView();
                
                // ربط الأحداث
                BindEvents();
                
                // تركيز على مربع البحث
                txtSearch.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// إعداد DataGridView
        /// </summary>
        private void SetupDataGridView()
        {
            try
            {
                dgvAccounts.AutoGenerateColumns = false;
                dgvAccounts.AllowUserToAddRows = false;
                dgvAccounts.AllowUserToDeleteRows = false;
                dgvAccounts.ReadOnly = true;
                dgvAccounts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                dgvAccounts.MultiSelect = false;
                dgvAccounts.RowHeadersVisible = false;
                dgvAccounts.BackgroundColor = Color.White;
                dgvAccounts.BorderStyle = BorderStyle.Fixed3D;
                dgvAccounts.GridColor = Color.LightGray;
                dgvAccounts.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
                
                // إضافة الأعمدة
                dgvAccounts.Columns.Clear();
                
                // عمود رمز الحساب
                var accountCodeColumn = new DataGridViewTextBoxColumn
                {
                    Name = "AccountCode",
                    HeaderText = "رمز الحساب",
                    DataPropertyName = "AccountCode",
                    Width = 120,
                    DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
                };
                dgvAccounts.Columns.Add(accountCodeColumn);
                
                // عمود اسم الحساب العربي
                var accountNameArColumn = new DataGridViewTextBoxColumn
                {
                    Name = "AccountNameAr",
                    HeaderText = "اسم الحساب",
                    DataPropertyName = "AccountNameAr",
                    Width = 300,
                    DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
                };
                dgvAccounts.Columns.Add(accountNameArColumn);
                
                // عمود نوع الحساب
                var accountTypeColumn = new DataGridViewTextBoxColumn
                {
                    Name = "AccountType",
                    HeaderText = "النوع",
                    DataPropertyName = "AccountTypeDisplayName",
                    Width = 100,
                    DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
                };
                dgvAccounts.Columns.Add(accountTypeColumn);
                
                // عمود طبيعة الحساب
                var accountNatureColumn = new DataGridViewTextBoxColumn
                {
                    Name = "AccountNature",
                    HeaderText = "الطبيعة",
                    DataPropertyName = "AccountNatureDisplayName",
                    Width = 80,
                    DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
                };
                dgvAccounts.Columns.Add(accountNatureColumn);
                
                // عمود المستوى
                var levelColumn = new DataGridViewTextBoxColumn
                {
                    Name = "Level",
                    HeaderText = "المستوى",
                    DataPropertyName = "LevelDisplayName",
                    Width = 80,
                    DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
                };
                dgvAccounts.Columns.Add(levelColumn);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد الجدول: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// ربط الأحداث
        /// </summary>
        private void BindEvents()
        {
            try
            {
                // أحداث البحث
                txtSearch.TextChanged += TxtSearch_TextChanged;
                txtSearch.KeyDown += TxtSearch_KeyDown;
                
                // أحداث الجدول
                dgvAccounts.CellDoubleClick += DgvAccounts_CellDoubleClick;
                dgvAccounts.KeyDown += DgvAccounts_KeyDown;
                
                // أحداث الأزرار
                btnSelect.Click += BtnSelect_Click;
                btnCancel.Click += BtnCancel_Click;
                
                // أحداث النموذج
                this.KeyDown += AccountLookupForm_KeyDown;
                this.Load += AccountLookupForm_Load;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ربط الأحداث: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        #endregion

        #region Data Loading
        
        /// <summary>
        /// تحميل الحسابات
        /// </summary>
        private void LoadAccounts()
        {
            try
            {
                _accounts = ChartOfAccountsService.GetAllAccounts()
                    .Where(a => a.LevelType == AccountLevelType.Detail && a.IsActive) // الحسابات التفصيلية النشطة فقط
                    .OrderBy(a => a.AccountCode)
                    .ToList();
                
                _filteredAccounts = new List<ChartOfAccount>(_accounts);
                RefreshGrid();
                
                // عرض عدد الحسابات
                lblAccountCount.Text = $"عدد الحسابات: {_filteredAccounts.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _accounts = new List<ChartOfAccount>();
                _filteredAccounts = new List<ChartOfAccount>();
            }
        }
        
        /// <summary>
        /// تحديث الجدول
        /// </summary>
        private void RefreshGrid()
        {
            try
            {
                dgvAccounts.DataSource = null;
                dgvAccounts.DataSource = _filteredAccounts;
                
                if (_filteredAccounts.Any())
                {
                    dgvAccounts.Rows[0].Selected = true;
                    dgvAccounts.CurrentCell = dgvAccounts.Rows[0].Cells[0];
                }
                
                lblAccountCount.Text = $"عدد الحسابات: {_filteredAccounts.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الجدول: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        #endregion

        #region Search and Filter
        
        /// <summary>
        /// تطبيق الفلتر
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        private void ApplyFilter(string searchText)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    _filteredAccounts = new List<ChartOfAccount>(_accounts);
                }
                else
                {
                    searchText = searchText.Trim().ToLower();
                    _filteredAccounts = _accounts.Where(a =>
                        a.AccountCode.ToLower().Contains(searchText) ||
                        a.AccountNameAr.ToLower().Contains(searchText) ||
                        (!string.IsNullOrEmpty(a.AccountNameEn) && a.AccountNameEn.ToLower().Contains(searchText))
                    ).ToList();
                }
                
                RefreshGrid();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        #endregion

        #region Selection
        
        /// <summary>
        /// تحديد الحساب
        /// </summary>
        private void SelectAccount()
        {
            try
            {
                if (dgvAccounts.CurrentRow != null)
                {
                    SelectedAccount = dgvAccounts.CurrentRow.DataBoundItem as ChartOfAccount;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("يرجى تحديد حساب", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد الحساب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        #endregion

        #region Event Handlers
        
        private void AccountLookupForm_Load(object sender, EventArgs e)
        {
            txtSearch.Focus();
        }
        
        private void AccountLookupForm_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Escape:
                    this.DialogResult = DialogResult.Cancel;
                    this.Close();
                    break;
                case Keys.Enter:
                    if (dgvAccounts.Focused)
                        SelectAccount();
                    break;
                case Keys.F3:
                    txtSearch.Focus();
                    break;
            }
        }
        
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            ApplyFilter(txtSearch.Text);
        }
        
        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Down:
                    if (dgvAccounts.Rows.Count > 0)
                    {
                        dgvAccounts.Focus();
                        dgvAccounts.CurrentCell = dgvAccounts.Rows[0].Cells[0];
                    }
                    e.Handled = true;
                    break;
                case Keys.Enter:
                    if (dgvAccounts.Rows.Count > 0)
                    {
                        dgvAccounts.Focus();
                        dgvAccounts.CurrentCell = dgvAccounts.Rows[0].Cells[0];
                    }
                    e.Handled = true;
                    break;
            }
        }
        
        private void DgvAccounts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
                SelectAccount();
        }
        
        private void DgvAccounts_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    SelectAccount();
                    e.Handled = true;
                    break;
                case Keys.F3:
                    txtSearch.Focus();
                    e.Handled = true;
                    break;
            }
        }
        
        private void BtnSelect_Click(object sender, EventArgs e)
        {
            SelectAccount();
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
        
        #endregion
    }
}
