using System;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج بيانات مركز التكلفة
    /// </summary>
    public class CostCenter
    {
        public int CostCenterId { get; set; }
        public string CostCenterCode { get; set; }
        public string CostCenterName { get; set; }
        public string CostCenterNameAr { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }

        public CostCenter()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
        }
    }
}
