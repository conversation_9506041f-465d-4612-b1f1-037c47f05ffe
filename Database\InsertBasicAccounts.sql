-- ===================================================================
-- إدراج الحسابات الأساسية لنظام إدارة الأوقاف
-- ===================================================================

USE AwqafManagement;
GO

-- إدراج الحسابات الرئيسية
INSERT INTO ChartOfAccounts (AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, CurrencyCode, OpeningBalance, CurrentBalance, CreatedBy)
VALUES 
-- الأصول
('1', 'Assets', N'الأصول', 1, 1, NULL, 1, 1, 1, 0, 'SAR', 0, 0, 1),
('1.1', 'Current Assets', N'الأصول المتداولة', 1, 1, 1, 2, 1, 1, 0, 'SAR', 0, 0, 1),
('1.1.1', 'Cash and Cash Equivalents', N'النقدية وما في حكمها', 1, 1, 2, 3, 1, 1, 0, 'SAR', 0, 0, 1),
('1.1.1.1', 'Cash in Hand', N'النقدية في الصندوق', 1, 1, 3, 4, 0, 1, 1, 'SAR', 0, 0, 1),
('1.1.1.2', 'Bank Account - Main', N'البنك الرئيسي', 1, 1, 3, 4, 0, 1, 1, 'SAR', 0, 0, 1),

-- الخصوم
('2', 'Liabilities', N'الخصوم', 2, 2, NULL, 1, 1, 1, 0, 'SAR', 0, 0, 1),
('2.1', 'Current Liabilities', N'الخصوم المتداولة', 2, 2, 6, 2, 1, 1, 0, 'SAR', 0, 0, 1),
('2.1.1', 'Accounts Payable', N'الحسابات الدائنة', 2, 2, 7, 3, 0, 1, 1, 'SAR', 0, 0, 1),

-- حقوق الملكية
('3', 'Equity', N'حقوق الملكية', 3, 3, NULL, 1, 1, 1, 0, 'SAR', 0, 0, 1),
('3.1', 'Capital', N'رأس المال', 3, 3, 9, 2, 0, 1, 1, 'SAR', 0, 0, 1),

-- الإيرادات
('4', 'Revenue', N'الإيرادات', 4, 4, NULL, 1, 1, 1, 0, 'SAR', 0, 0, 1),
('4.1', 'Rental Income', N'إيرادات الإيجارات', 4, 4, 11, 2, 0, 1, 1, 'SAR', 0, 0, 1),
('4.2', 'Property Sales', N'إيرادات بيع العقارات', 4, 4, 11, 2, 0, 1, 1, 'SAR', 0, 0, 1),

-- المصروفات
('5', 'Expenses', N'المصروفات', 5, 5, NULL, 1, 1, 1, 0, 'SAR', 0, 0, 1),
('5.1', 'Operating Expenses', N'المصروفات التشغيلية', 5, 5, 14, 2, 1, 1, 0, 'SAR', 0, 0, 1),
('5.1.1', 'Maintenance Expenses', N'مصروفات الصيانة', 5, 5, 15, 3, 0, 1, 1, 'SAR', 0, 0, 1),
('5.1.2', 'Administrative Expenses', N'المصروفات الإدارية', 5, 5, 15, 3, 0, 1, 1, 'SAR', 0, 0, 1);

PRINT N'تم إدراج الحسابات الأساسية بنجاح';
GO

-- التحقق من البيانات المدرجة
SELECT 
    AccountCode,
    AccountNameAr,
    AccountLevel,
    IsParent,
    AllowPosting
FROM ChartOfAccounts 
ORDER BY AccountCode;

PRINT N'تم عرض الحسابات المدرجة';
GO
