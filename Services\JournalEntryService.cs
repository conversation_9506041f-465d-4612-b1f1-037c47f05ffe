using System;
using System.Collections.Generic;
using System.Linq;
using AwqafManagement.Models;
using AwqafManagement.DataAccess;

namespace AwqafManagement.Services
{
    public static class JournalEntryService
    {
        #region Journal Entry Management
        
        /// <summary>
        /// إنشاء قيد يومي جديد
        /// </summary>
        /// <param name="journalEntry">بيانات القيد</param>
        /// <returns>معرف القيد المنشأ</returns>
        public static int CreateJournalEntry(JournalEntry journalEntry)
        {
            try
            {
                if (journalEntry == null)
                    throw new ArgumentNullException(nameof(journalEntry));

                // التحقق من صحة البيانات
                ValidateJournalEntry(journalEntry);

                // إنشاء القيد في قاعدة البيانات
                var journalEntryDataAccess = new JournalEntryDataAccess();
                int journalEntryId = journalEntryDataAccess.CreateJournalEntry(journalEntry);

                return journalEntryId;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء القيد اليومي: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث قيد يومي موجود
        /// </summary>
        /// <param name="journalEntry">بيانات القيد المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateJournalEntry(JournalEntry journalEntry)
        {
            try
            {
                if (journalEntry == null)
                    throw new ArgumentNullException(nameof(journalEntry));

                if (journalEntry.JournalEntryId <= 0)
                    throw new ArgumentException("معرف القيد غير صحيح");

                // التحقق من أن القيد لم يتم ترحيله
                if (journalEntry.Status == JournalStatus.Posted)
                    throw new InvalidOperationException("لا يمكن تعديل قيد مرحل");

                // التحقق من صحة البيانات
                ValidateJournalEntry(journalEntry);

                // تحديث القيد في قاعدة البيانات
                var journalEntryDataAccess = new JournalEntryDataAccess();
                return journalEntryDataAccess.UpdateJournalEntry(journalEntry);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث القيد اليومي: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف قيد يومي
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteJournalEntry(int journalEntryId)
        {
            try
            {
                if (journalEntryId <= 0)
                    throw new ArgumentException("معرف القيد غير صحيح");

                // التحقق من حالة القيد
                var journalEntry = GetJournalEntryById(journalEntryId);
                if (journalEntry == null)
                    throw new ArgumentException("القيد غير موجود");

                if (journalEntry.Status == JournalStatus.Posted)
                    throw new InvalidOperationException("لا يمكن حذف قيد مرحل");

                // حذف القيد من قاعدة البيانات
                var journalEntryDataAccess = new JournalEntryDataAccess();
                return journalEntryDataAccess.DeleteJournalEntry(journalEntryId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف القيد اليومي: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// ترحيل قيد يومي
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>true إذا تم الترحيل بنجاح</returns>
        public static bool PostJournalEntry(int journalEntryId)
        {
            try
            {
                if (journalEntryId <= 0)
                    throw new ArgumentException("معرف القيد غير صحيح");

                // الحصول على القيد
                var journalEntry = GetJournalEntryById(journalEntryId);
                if (journalEntry == null)
                    throw new ArgumentException("القيد غير موجود");

                if (journalEntry.Status == JournalStatus.Posted)
                    throw new InvalidOperationException("القيد مرحل مسبقاً");

                // التحقق من توازن القيد
                if (!journalEntry.IsBalanced)
                    throw new InvalidOperationException("لا يمكن ترحيل قيد غير متوازن");

                // ترحيل القيد
                var journalEntryDataAccess = new JournalEntryDataAccess();
                return journalEntryDataAccess.PostJournalEntry(journalEntryId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في ترحيل القيد اليومي: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على قيد يومي بالمعرف
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>بيانات القيد</returns>
        public static JournalEntry GetJournalEntryById(int journalEntryId)
        {
            try
            {
                if (journalEntryId <= 0)
                    return null;

                var journalEntryDataAccess = new JournalEntryDataAccess();
                return journalEntryDataAccess.GetJournalEntryById(journalEntryId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على القيد اليومي: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على جميع القيود اليومية
        /// </summary>
        /// <returns>قائمة القيود</returns>
        public static List<JournalEntry> GetAllJournalEntries()
        {
            try
            {
                var journalEntryDataAccess = new JournalEntryDataAccess();
                return journalEntryDataAccess.GetAllJournalEntries() ?? new List<JournalEntry>();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على القيود اليومية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// البحث في القيود اليومية
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>قائمة القيود المطابقة</returns>
        public static List<JournalEntry> SearchJournalEntries(JournalEntrySearchCriteria searchCriteria)
        {
            try
            {
                if (searchCriteria == null)
                    return GetAllJournalEntries();

                var journalEntryDataAccess = new JournalEntryDataAccess();
                return journalEntryDataAccess.SearchJournalEntries(searchCriteria) ?? new List<JournalEntry>();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في القيود اليومية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// توليد رقم قيد تلقائي
        /// </summary>
        /// <param name="journalType">نوع القيد</param>
        /// <returns>رقم القيد</returns>
        public static string GenerateJournalNumber(JournalType journalType = JournalType.General)
        {
            try
            {
                var prefix = GetJournalTypePrefix(journalType);
                var dateString = DateTime.Now.ToString("yyyyMM");
                var timeString = DateTime.Now.ToString("ddHHmm");
                
                return $"{prefix}{dateString}{timeString}";
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في توليد رقم القيد: {ex.Message}", ex);
            }
        }
        #endregion

        #region Validation
        
        /// <summary>
        /// التحقق من صحة بيانات القيد
        /// </summary>
        /// <param name="journalEntry">بيانات القيد</param>
        private static void ValidateJournalEntry(JournalEntry journalEntry)
        {
            if (string.IsNullOrWhiteSpace(journalEntry.JournalNumber))
                throw new ArgumentException("رقم القيد مطلوب");

            if (string.IsNullOrWhiteSpace(journalEntry.GeneralDescription))
                throw new ArgumentException("البيان العام مطلوب");

            if (journalEntry.JournalEntryDetails == null || !journalEntry.JournalEntryDetails.Any())
                throw new ArgumentException("تفاصيل القيد مطلوبة");

            // التحقق من وجود مبالغ صحيحة
            bool hasValidAmounts = journalEntry.JournalEntryDetails.Any(d => d.DebitAmount > 0 || d.CreditAmount > 0);
            if (!hasValidAmounts)
                throw new ArgumentException("يجب إدخال مبالغ صحيحة في تفاصيل القيد");

            // التحقق من عدم وجود مدين ودائن في نفس السطر
            var invalidDetails = journalEntry.JournalEntryDetails.Where(d => d.DebitAmount > 0 && d.CreditAmount > 0);
            if (invalidDetails.Any())
                throw new ArgumentException("لا يمكن أن يكون هناك مدين ودائن في نفس السطر");
        }

        /// <summary>
        /// الحصول على بادئة نوع القيد
        /// </summary>
        /// <param name="journalType">نوع القيد</param>
        /// <returns>البادئة</returns>
        private static string GetJournalTypePrefix(JournalType journalType)
        {
            return journalType switch
            {
                JournalType.General => "JE",
                JournalType.Recurring => "RJ",
                JournalType.Reversing => "RV",
                JournalType.Closing => "CJ",
                JournalType.Adjusting => "AJ",
                _ => "JE"
            };
        }
        #endregion
    }

    /// <summary>
    /// معايير البحث في القيود اليومية
    /// </summary>
    public class JournalEntrySearchCriteria
    {
        public string JournalNumber { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public JournalType? JournalType { get; set; }
        public JournalStatus? Status { get; set; }
        public int? AccountId { get; set; }
        public int? CostCenterId { get; set; }
        public string Description { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
    }
}
