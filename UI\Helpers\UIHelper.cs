using System;
using System.Drawing;
using System.Windows.Forms;

namespace Awqaf_Managment.UI.Helpers
{
    /// <summary>
    /// مساعد واجهة المستخدم
    /// UI Helper Class
    /// </summary>
    public static class UIHelper
    {
        /// <summary>
        /// تطبيق التصميم العصري على النموذج
        /// Apply modern design to form
        /// </summary>
        /// <param name="form">النموذج</param>
        public static void ApplyModernDesign(Form form)
        {
            if (form == null) return;

            form.BackColor = Color.FromArgb(248, 249, 250);
            form.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
        }

        /// <summary>
        /// تطبيق تصميم الأزرار العصري
        /// Apply modern button design
        /// </summary>
        /// <param name="button">الزر</param>
        /// <param name="backColor">لون الخلفية</param>
        /// <param name="foreColor">لون النص</param>
        public static void ApplyButtonStyle(Button button, Color backColor, Color foreColor)
        {
            if (button == null) return;

            button.BackColor = backColor;
            button.ForeColor = foreColor;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
        }

        /// <summary>
        /// تطبيق تصميم GroupBox العصري
        /// Apply modern GroupBox design
        /// </summary>
        /// <param name="groupBox">مجموعة التحكم</param>
        public static void ApplyGroupBoxStyle(GroupBox groupBox)
        {
            if (groupBox == null) return;

            groupBox.BackColor = Color.White;
            groupBox.ForeColor = Color.FromArgb(52, 58, 64);
            groupBox.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
        }

        /// <summary>
        /// تطبيق تصميم TextBox العصري
        /// Apply modern TextBox design
        /// </summary>
        /// <param name="textBox">مربع النص</param>
        public static void ApplyTextBoxStyle(TextBox textBox)
        {
            if (textBox == null) return;

            textBox.BackColor = Color.White;
            textBox.ForeColor = Color.FromArgb(52, 58, 64);
            textBox.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            textBox.BorderStyle = BorderStyle.FixedSingle;
        }

        /// <summary>
        /// تطبيق تصميم ComboBox العصري
        /// Apply modern ComboBox design
        /// </summary>
        /// <param name="comboBox">قائمة منسدلة</param>
        public static void ApplyComboBoxStyle(ComboBox comboBox)
        {
            if (comboBox == null) return;

            comboBox.BackColor = Color.White;
            comboBox.ForeColor = Color.FromArgb(52, 58, 64);
            comboBox.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            comboBox.FlatStyle = FlatStyle.Flat;
        }

        /// <summary>
        /// تطبيق تصميم TreeView العصري
        /// Apply modern TreeView design
        /// </summary>
        /// <param name="treeView">عرض الشجرة</param>
        public static void ApplyTreeViewStyle(TreeView treeView)
        {
            if (treeView == null) return;

            treeView.BackColor = Color.White;
            treeView.ForeColor = Color.FromArgb(52, 58, 64);
            treeView.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            treeView.BorderStyle = BorderStyle.FixedSingle;
            treeView.ShowLines = true;
            treeView.ShowPlusMinus = true;
            treeView.ShowRootLines = true;
        }

        /// <summary>
        /// إظهار رسالة خطأ بالعربية
        /// Show Arabic error message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        public static void ShowErrorMessage(string message, string title = "خطأ")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// إظهار رسالة تحذير بالعربية
        /// Show Arabic warning message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        public static void ShowWarningMessage(string message, string title = "تحذير")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// إظهار رسالة معلومات بالعربية
        /// Show Arabic information message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        public static void ShowInfoMessage(string message, string title = "معلومات")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// إظهار رسالة تأكيد بالعربية
        /// Show Arabic confirmation message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <returns>نتيجة الحوار</returns>
        public static DialogResult ShowConfirmMessage(string message, string title = "تأكيد")
        {
            return MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);
        }
    }
}
