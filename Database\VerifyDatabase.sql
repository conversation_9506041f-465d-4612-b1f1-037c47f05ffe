-- ===================================================================
-- سكريبت التحقق من قاعدة بيانات نظام إدارة الأوقاف
-- ===================================================================

USE AwqafManagement;
GO

PRINT N'=== تقرير حالة قاعدة البيانات ===';
PRINT N'';

-- 1. التحقق من الجداول
PRINT N'1. الجداول الموجودة:';
SELECT 
    TABLE_NAME as N'اسم الجدول',
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as N'عدد الأعمدة'
FROM INFORMATION_SCHEMA.TABLES t
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

PRINT N'';

-- 2. التحقق من المستخدمين
PRINT N'2. المستخدمين:';
SELECT 
    Username as N'اسم المستخدم',
    FullName as N'الاسم الكامل',
    CASE WHEN IsActive = 1 THEN N'نشط' ELSE N'غير نشط' END as N'الحالة',
    CASE WHEN IsLocked = 1 THEN N'مقفل' ELSE N'غير مقفل' END as N'القفل'
FROM Users;

PRINT N'';

-- 3. التحقق من الأدوار
PRINT N'3. الأدوار:';
SELECT 
    RoleName as N'اسم الدور',
    RoleNameAr as N'الاسم العربي',
    CASE WHEN IsActive = 1 THEN N'نشط' ELSE N'غير نشط' END as N'الحالة'
FROM Roles;

PRINT N'';

-- 4. التحقق من الصلاحيات
PRINT N'4. الصلاحيات:';
SELECT 
    COUNT(*) as N'عدد الصلاحيات',
    COUNT(CASE WHEN IsActive = 1 THEN 1 END) as N'النشطة'
FROM Permissions;

PRINT N'';

-- 5. التحقق من أنواع الحسابات
PRINT N'5. أنواع الحسابات:';
SELECT
    AccountTypeName as N'النوع',
    AccountTypeNameAr as N'الاسم العربي'
FROM AccountTypes
ORDER BY AccountTypeId;

PRINT N'';

-- 6. التحقق من مجموعات الحسابات
PRINT N'6. مجموعات الحسابات:';
SELECT
    GroupName as N'المجموعة',
    GroupNameAr as N'الاسم العربي'
FROM AccountGroups
ORDER BY AccountGroupId;

PRINT N'';

-- 7. التحقق من الحسابات
PRINT N'7. الحسابات:';
SELECT 
    COUNT(*) as N'إجمالي الحسابات',
    COUNT(CASE WHEN IsParent = 1 THEN 1 END) as N'الحسابات الرئيسية',
    COUNT(CASE WHEN AllowPosting = 1 THEN 1 END) as N'حسابات القيد',
    COUNT(CASE WHEN IsActive = 1 THEN 1 END) as N'النشطة'
FROM ChartOfAccounts;

PRINT N'';

-- 8. عرض هيكل الحسابات
PRINT N'8. هيكل الحسابات الرئيسية:';
SELECT 
    AccountCode as N'رمز الحساب',
    AccountNameAr as N'اسم الحساب',
    AccountLevel as N'المستوى',
    CASE WHEN IsParent = 1 THEN N'رئيسي' ELSE N'فرعي' END as N'النوع',
    CASE WHEN AllowPosting = 1 THEN N'نعم' ELSE N'لا' END as N'يسمح بالقيد'
FROM ChartOfAccounts
WHERE AccountLevel <= 2
ORDER BY AccountCode;

PRINT N'';
PRINT N'=== انتهى التقرير ===';
GO
