using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;
using Awqaf_Managment.Common;
using Awqaf_Managment.Common.Helpers;
using Awqaf_Managment.Services.Security;
using Awqaf_Managment.UI.Forms.Security;
using Awqaf_Managment.UI.Forms.Accounting;

namespace Awqaf_Managment.UI.Forms
{
    public partial class MainForm : Form
    {
        private IconButton currentButton;
        private Panel leftBorderButton;
        private Form currentChildForm;

        public MainForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);
            
            // تطبيق الخط العربي
            UIHelper.ApplyArabicFont(this);
            
            // إنشاء الحد الأيسر للزر النشط
            leftBorderButton = new Panel();
            leftBorderButton.Size = new Size(7, Constants.ButtonHeight);
            pnlMenu.Controls.Add(leftBorderButton);
            
            // تخصيص الألوان
            this.BackColor = Constants.BackgroundColor;
            pnlMenu.BackColor = Constants.SidebarColor;
            pnlTitleBar.BackColor = Constants.PrimaryColor;
            
            // إعداد أحداث الأزرار
            SetupMenuButtons();
            
            // تحديث معلومات المستخدم
            UpdateUserInfo();

            // عرض الصفحة الرئيسية افتراضياً
            ActivateButton(btnDashboard, Constants.PrimaryColor);
            OpenChildForm(new DashboardForm());
        }

        private void SetupMenuButtons()
        {
            btnDashboard.Click += (s, e) => {
                ActivateButton(s as IconButton, Constants.PrimaryColor);
                OpenChildForm(new DashboardForm());
            };
            btnProperties.Click += (s, e) => ActivateButton(s as IconButton, Color.FromArgb(76, 175, 80));
            btnCustomers.Click += (s, e) => ActivateButton(s as IconButton, Color.FromArgb(156, 39, 176));
            btnContracts.Click += (s, e) => ActivateButton(s as IconButton, Color.FromArgb(255, 152, 0));
            btnPayments.Click += (s, e) => ActivateButton(s as IconButton, Color.FromArgb(255, 193, 7));
            btnAccounting.Click += (s, e) => {
                ActivateButton(s as IconButton, Color.FromArgb(63, 81, 181));
                ShowAccountingMenu();
            };
            btnInventory.Click += (s, e) => ActivateButton(s as IconButton, Color.FromArgb(121, 85, 72));
            btnEmployees.Click += (s, e) => ActivateButton(s as IconButton, Color.FromArgb(96, 125, 139));
            btnAssets.Click += (s, e) => ActivateButton(s as IconButton, Color.FromArgb(139, 69, 19));
            btnReports.Click += (s, e) => ActivateButton(s as IconButton, Color.FromArgb(244, 67, 54));
            btnSettings.Click += (s, e) => {
                ActivateButton(s as IconButton, Color.FromArgb(158, 158, 158));
                OpenChildForm(new UserManagementForm());
            };
            btnLogout.Click += BtnLogout_Click;
        }

        private void ActivateButton(IconButton senderButton, Color color)
        {
            if (senderButton != null)
            {
                DisableButton();
                currentButton = senderButton;
                currentButton.BackColor = Color.FromArgb(37, 36, 81);
                currentButton.ForeColor = color;
                currentButton.TextAlign = ContentAlignment.MiddleCenter;
                currentButton.IconColor = color;
                currentButton.TextImageRelation = TextImageRelation.TextBeforeImage;
                currentButton.ImageAlign = ContentAlignment.MiddleRight;

                // تحديث الحد الأيسر
                leftBorderButton.BackColor = color;
                leftBorderButton.Location = new Point(0, currentButton.Location.Y);
                leftBorderButton.Visible = true;
                leftBorderButton.BringToFront();

                // تحديث عنوان النافذة
                lblTitle.Text = currentButton.Text;
                pnlTitleBar.BackColor = color;
                pnlLogo.BackColor = Color.FromArgb(39, 39, 58);
            }
        }

        private void DisableButton()
        {
            if (currentButton != null)
            {
                currentButton.BackColor = Constants.SidebarColor;
                currentButton.ForeColor = Color.Gainsboro;
                currentButton.TextAlign = ContentAlignment.MiddleLeft;
                currentButton.IconColor = Color.Gainsboro;
                currentButton.TextImageRelation = TextImageRelation.ImageBeforeText;
                currentButton.ImageAlign = ContentAlignment.MiddleLeft;
            }
        }

        private void OpenChildForm(Form childForm)
        {
            if (currentChildForm != null)
            {
                currentChildForm.Close();
            }
            currentChildForm = childForm;
            childForm.TopLevel = false;
            childForm.FormBorderStyle = FormBorderStyle.None;
            childForm.Dock = DockStyle.Fill;
            pnlDesktop.Controls.Add(childForm);
            pnlDesktop.Tag = childForm;
            childForm.BringToFront();
            childForm.Show();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            if (UIHelper.ShowConfirm("هل تريد تسجيل الخروج؟") == DialogResult.Yes)
            {
                this.Hide();
                var loginForm = new LoginForm();
                loginForm.ShowDialog();
                this.Close();
            }
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Maximized;
        }

        private void Reset()
        {
            DisableButton();
            leftBorderButton.Visible = false;
            lblTitle.Text = "الصفحة الرئيسية";
            pnlTitleBar.BackColor = Constants.PrimaryColor;
            pnlLogo.BackColor = Color.FromArgb(39, 39, 58);
            currentButton = null;
        }

        private void UpdateUserInfo()
        {
            if (AuthenticationService.CurrentUser != null)
            {
                // تحديث عنوان النافذة ليشمل اسم المستخدم
                this.Text = $"نظام إدارة الأوقاف - {AuthenticationService.CurrentUser.FullName}";

                // إخفاء الأزرار التي لا يملك المستخدم صلاحية عليها
                ApplyUserPermissions();
            }
        }

        private void ApplyUserPermissions()
        {
            // مؤقتاً: إظهار جميع الأزرار للاختبار
            // TODO: تطبيق نظام الصلاحيات بشكل كامل لاحقاً

            btnProperties.Visible = true; // AuthenticationService.HasModulePermission("Properties");
            btnCustomers.Visible = true; // AuthenticationService.HasModulePermission("Customers");
            btnContracts.Visible = true; // AuthenticationService.HasModulePermission("Contracts");
            btnPayments.Visible = true; // AuthenticationService.HasModulePermission("Payments");
            btnAccounting.Visible = true; // AuthenticationService.HasModulePermission("Accounting");
            btnInventory.Visible = true; // AuthenticationService.HasModulePermission("Inventory");
            btnEmployees.Visible = true; // AuthenticationService.HasModulePermission("Employees");
            btnAssets.Visible = true; // AuthenticationService.HasModulePermission("Assets");
            btnReports.Visible = true; // AuthenticationService.HasModulePermission("Reports");
            btnSettings.Visible = true; // AuthenticationService.HasModulePermission("Settings");
        }

        private void ShowAccountingMenu()
        {
            var contextMenu = new ContextMenuStrip();
            contextMenu.BackColor = Color.FromArgb(51, 51, 76);
            contextMenu.ForeColor = Color.White;
            contextMenu.Font = new Font("Tahoma", 10F, FontStyle.Regular);

            // إضافة عناصر القائمة
            var chartOfAccountsItem = new ToolStripMenuItem("📊 الدليل المحاسبي");
            chartOfAccountsItem.Click += (s, e) => OpenChildForm(new Awqaf_Managment.UI.Forms.Accounting.ChartOfAccountsManagementForm());
            contextMenu.Items.Add(chartOfAccountsItem);

            var currencyManagementItem = new ToolStripMenuItem("💰 إدارة العملات");
            currencyManagementItem.Click += (s, e) => OpenChildForm(new CurrencyManagementForm());
            contextMenu.Items.Add(currencyManagementItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            var journalEntriesItem = new ToolStripMenuItem("📝 القيود اليومية");
            journalEntriesItem.Click += (s, e) => OpenChildForm(new AwqafManagement.UI.Forms.Accounting.JournalEntryForm());
            contextMenu.Items.Add(journalEntriesItem);

            var trialBalanceItem = new ToolStripMenuItem("⚖️ ميزان المراجعة");
            trialBalanceItem.Click += (s, e) => MessageBox.Show("قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            contextMenu.Items.Add(trialBalanceItem);

            // عرض القائمة بجانب زر المحاسبة
            var buttonLocation = btnAccounting.PointToScreen(new Point(btnAccounting.Width, 0));
            contextMenu.Show(buttonLocation);
        }

    }
}
