using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.DataAccess.Accounting;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    /// <summary>
    /// نموذج إدارة العملات الاحترافي
    /// Professional Currency Management Form
    /// </summary>
    public partial class CurrencyManagementForm : Form
    {
        #region Fields
        private List<Currency> _currencies;
        private Currency _currentCurrency;
        private bool _isEditMode;
        private bool _isAddMode;
        #endregion

        #region Constructor
        public CurrencyManagementForm()
        {
            InitializeComponent();
            InitializeForm();
        }
        #endregion

        #region Initialization
        private void InitializeForm()
        {
            try
            {
                SetupDataGridView();
                LoadDefaultCurrencies();
                ResetForm();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error, 
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void SetupDataGridView()
        {
            // إعدادات عامة
            dgvCurrencies.AutoGenerateColumns = false;
            dgvCurrencies.AllowUserToAddRows = false;
            dgvCurrencies.AllowUserToDeleteRows = false;
            dgvCurrencies.ReadOnly = true;
            dgvCurrencies.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvCurrencies.MultiSelect = false;
            
            // إعدادات RTL والخط
            dgvCurrencies.RightToLeft = RightToLeft.Yes;
            dgvCurrencies.Font = new Font("Tahoma", 10F, FontStyle.Regular);
            dgvCurrencies.DefaultCellStyle.Font = new Font("Tahoma", 10F);
            dgvCurrencies.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 11F, FontStyle.Bold);
            dgvCurrencies.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgvCurrencies.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvCurrencies.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvCurrencies.ColumnHeadersHeight = 35;
            
            // مسح الأعمدة الموجودة
            dgvCurrencies.Columns.Clear();

            // إضافة الأعمدة
            dgvCurrencies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrencyCode",
                HeaderText = "رمز العملة",
                DataPropertyName = "CurrencyCode",
                Width = 100,
                ReadOnly = true
            });

            dgvCurrencies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CurrencyNameAr",
                HeaderText = "اسم العملة",
                DataPropertyName = "CurrencyNameAr",
                Width = 180,
                ReadOnly = true
            });

            dgvCurrencies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Symbol",
                HeaderText = "الرمز",
                DataPropertyName = "Symbol",
                Width = 80,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Tahoma", 12F, FontStyle.Bold)
                }
            });

            dgvCurrencies.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ExchangeRate",
                HeaderText = "سعر الصرف",
                DataPropertyName = "ExchangeRate",
                Width = 120,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N4",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            dgvCurrencies.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsBaseCurrency",
                HeaderText = "عملة أساسية",
                DataPropertyName = "IsBaseCurrency",
                Width = 100,
                ReadOnly = true
            });

            dgvCurrencies.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 80,
                ReadOnly = true
            });
        }

        private void LoadDefaultCurrencies()
        {
            try
            {
                // استخدم البيانات الافتراضية دائماً لضمان العرض
                _currencies = GetDefaultCurrencies();
                dgvCurrencies.DataSource = _currencies;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error, 
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private List<Currency> GetDefaultCurrencies()
        {
            return new List<Currency>
            {
                new Currency("SAR", "ريال سعودي", "ر.س", 1.0000m) 
                { 
                    CurrencyId = 1, 
                    IsBaseCurrency = true, 
                    CurrencyNameEn = "Saudi Riyal",
                    DecimalPlaces = 2,
                    IsActive = true
                },
                new Currency("USD", "دولار أمريكي", "$", 3.7500m) 
                { 
                    CurrencyId = 2, 
                    CurrencyNameEn = "US Dollar",
                    DecimalPlaces = 2,
                    IsActive = true
                },
                new Currency("EUR", "يورو", "€", 4.1000m) 
                { 
                    CurrencyId = 3, 
                    CurrencyNameEn = "Euro",
                    DecimalPlaces = 2,
                    IsActive = true
                },
                new Currency("GBP", "جنيه إسترليني", "£", 4.8500m) 
                { 
                    CurrencyId = 4, 
                    CurrencyNameEn = "British Pound",
                    DecimalPlaces = 2,
                    IsActive = true
                },
                new Currency("AED", "درهم إماراتي", "د.إ", 1.0200m) 
                { 
                    CurrencyId = 5, 
                    CurrencyNameEn = "UAE Dirham",
                    DecimalPlaces = 2,
                    IsActive = true
                },
                new Currency("KWD", "دينار كويتي", "د.ك", 12.2500m) 
                { 
                    CurrencyId = 6, 
                    CurrencyNameEn = "Kuwaiti Dinar",
                    DecimalPlaces = 3,
                    IsActive = true
                }
            };
        }
        #endregion

        #region Form Operations
        private void ResetForm()
        {
            _currentCurrency = null;
            _isEditMode = false;
            _isAddMode = false;
            
            // مسح الحقول
            txtCurrencyCode.Clear();
            txtCurrencyNameAr.Clear();
            txtCurrencyNameEn.Clear();
            txtSymbol.Clear();
            txtExchangeRate.Text = "1.0000";
            nudDecimalPlaces.Value = 2;
            chkIsBaseCurrency.Checked = false;
            chkIsActive.Checked = true;
            
            // تعطيل الحقول
            EnableControls(false);
            
            // إلغاء التحديد في الجدول
            dgvCurrencies.ClearSelection();
        }

        private void EnableControls(bool enabled)
        {
            txtCurrencyCode.Enabled = enabled;
            txtCurrencyNameAr.Enabled = enabled;
            txtCurrencyNameEn.Enabled = enabled;
            txtSymbol.Enabled = enabled;
            txtExchangeRate.Enabled = enabled;
            nudDecimalPlaces.Enabled = enabled;
            chkIsBaseCurrency.Enabled = enabled;
            chkIsActive.Enabled = enabled;
            
            // تغيير لون الخلفية
            Color backColor = enabled ? Color.White : Color.FromArgb(240, 240, 240);
            txtCurrencyCode.BackColor = backColor;
            txtCurrencyNameAr.BackColor = backColor;
            txtCurrencyNameEn.BackColor = backColor;
            txtSymbol.BackColor = backColor;
            txtExchangeRate.BackColor = backColor;
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = dgvCurrencies.SelectedRows.Count > 0;
            bool isOperationMode = _isAddMode || _isEditMode;

            btnAdd.Enabled = !isOperationMode;
            btnEdit.Enabled = hasSelection && !isOperationMode;
            btnDelete.Enabled = hasSelection && !isOperationMode;
            btnRefresh.Enabled = !isOperationMode;

            btnSave.Enabled = isOperationMode;
            btnCancel.Enabled = isOperationMode;
        }
        #endregion

        #region Event Handlers
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                _isAddMode = true;
                _isEditMode = false;
                _currentCurrency = new Currency();

                EnableControls(true);
                txtCurrencyCode.Focus();
                UpdateButtonStates();

                MessageBox.Show("يمكنك الآن إدخال بيانات العملة الجديدة", "إضافة عملة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تفعيل وضع الإضافة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvCurrencies.SelectedRows.Count > 0)
                {
                    var selectedCurrency = (Currency)dgvCurrencies.SelectedRows[0].DataBoundItem;
                    if (selectedCurrency != null)
                    {
                        _currentCurrency = selectedCurrency;
                        _isEditMode = true;
                        _isAddMode = false;

                        LoadCurrencyToForm(selectedCurrency);
                        EnableControls(true);
                        txtCurrencyCode.Enabled = false; // منع تعديل الرمز
                        txtCurrencyNameAr.Focus();
                        UpdateButtonStates();

                        MessageBox.Show("يمكنك الآن تعديل بيانات العملة", "تعديل عملة",
                            MessageBoxButtons.OK, MessageBoxIcon.Information,
                            MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تفعيل وضع التعديل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvCurrencies.SelectedRows.Count > 0)
                {
                    var selectedCurrency = (Currency)dgvCurrencies.SelectedRows[0].DataBoundItem;
                    if (selectedCurrency != null)
                    {
                        var result = MessageBox.Show(
                            $"هل أنت متأكد من حذف العملة '{selectedCurrency.CurrencyNameAr}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                            "تأكيد الحذف",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Warning,
                            MessageBoxDefaultButton.Button2,
                            MessageBoxOptions.RtlReading);

                        if (result == DialogResult.Yes)
                        {
                            _currencies.Remove(selectedCurrency);
                            dgvCurrencies.DataSource = null;
                            dgvCurrencies.DataSource = _currencies;
                            ResetForm();
                            UpdateButtonStates();

                            MessageBox.Show("تم حذف العملة بنجاح", "نجح الحذف",
                                MessageBoxButtons.OK, MessageBoxIcon.Information,
                                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العملة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                SaveFormToCurrency();

                if (_isAddMode)
                {
                    // إضافة عملة جديدة
                    _currentCurrency.CurrencyId = _currencies.Count > 0 ? _currencies.Max(c => c.CurrencyId) + 1 : 1;
                    _currencies.Add(_currentCurrency);

                    MessageBox.Show("تم إضافة العملة بنجاح", "نجح الحفظ",
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
                else if (_isEditMode)
                {
                    MessageBox.Show("تم تحديث العملة بنجاح", "نجح الحفظ",
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }

                // تحديث العرض
                dgvCurrencies.DataSource = null;
                dgvCurrencies.DataSource = _currencies;
                ResetForm();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ العملة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            ResetForm();
            UpdateButtonStates();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadDefaultCurrencies();
            ResetForm();
            UpdateButtonStates();
        }

        private void DgvCurrencies_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }
        #endregion

        #region Helper Methods
        private bool ValidateInput()
        {
            // التحقق من رمز العملة
            if (string.IsNullOrWhiteSpace(txtCurrencyCode.Text))
            {
                MessageBox.Show("يرجى إدخال رمز العملة", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtCurrencyCode.Focus();
                return false;
            }

            // التحقق من تكرار رمز العملة
            if (_isAddMode && _currencies.Any(c => c.CurrencyCode.Equals(txtCurrencyCode.Text.Trim(), StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("رمز العملة موجود مسبقاً", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtCurrencyCode.Focus();
                return false;
            }

            // التحقق من اسم العملة بالعربية
            if (string.IsNullOrWhiteSpace(txtCurrencyNameAr.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العملة بالعربية", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtCurrencyNameAr.Focus();
                return false;
            }

            // التحقق من الرمز
            if (string.IsNullOrWhiteSpace(txtSymbol.Text))
            {
                MessageBox.Show("يرجى إدخال رمز العملة", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtSymbol.Focus();
                return false;
            }

            // التحقق من سعر الصرف
            if (!decimal.TryParse(txtExchangeRate.Text, out decimal rate) || rate <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر صرف صحيح", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtExchangeRate.Focus();
                return false;
            }

            return true;
        }

        private void LoadCurrencyToForm(Currency currency)
        {
            txtCurrencyCode.Text = currency.CurrencyCode;
            txtCurrencyNameAr.Text = currency.CurrencyNameAr;
            txtCurrencyNameEn.Text = currency.CurrencyNameEn ?? "";
            txtSymbol.Text = currency.Symbol;
            txtExchangeRate.Text = currency.ExchangeRate.ToString("F4");
            nudDecimalPlaces.Value = currency.DecimalPlaces;
            chkIsBaseCurrency.Checked = currency.IsBaseCurrency;
            chkIsActive.Checked = currency.IsActive;
        }

        private void SaveFormToCurrency()
        {
            _currentCurrency.CurrencyCode = txtCurrencyCode.Text.Trim().ToUpper();
            _currentCurrency.CurrencyNameAr = txtCurrencyNameAr.Text.Trim();
            _currentCurrency.CurrencyNameEn = txtCurrencyNameEn.Text.Trim();
            _currentCurrency.Symbol = txtSymbol.Text.Trim();
            _currentCurrency.ExchangeRate = decimal.Parse(txtExchangeRate.Text);
            _currentCurrency.DecimalPlaces = (int)nudDecimalPlaces.Value;
            _currentCurrency.IsBaseCurrency = chkIsBaseCurrency.Checked;
            _currentCurrency.IsActive = chkIsActive.Checked;
        }
        #endregion
    }
}
