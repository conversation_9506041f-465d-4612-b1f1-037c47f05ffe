using System;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// مستوى الحساب
    /// Account Level Type
    /// </summary>
    public enum AccountLevelType
    {
        /// <summary>
        /// حساب رئيسي
        /// Main Account
        /// </summary>
        Main = 1,

        /// <summary>
        /// حساب فرعي
        /// Sub Account
        /// </summary>
        Sub = 2,

        /// <summary>
        /// حساب تفصيلي
        /// Detail Account
        /// </summary>
        Detail = 3
    }

    /// <summary>
    /// طبيعة الحساب
    /// Account Nature
    /// </summary>
    public enum AccountNature
    {
        /// <summary>
        /// مدين
        /// Debit
        /// </summary>
        Debit = 1,

        /// <summary>
        /// دائن
        /// Credit
        /// </summary>
        Credit = 2
    }

    /// <summary>
    /// حالة الحساب
    /// Account Status
    /// </summary>
    public enum AccountStatus
    {
        /// <summary>
        /// نشط
        /// Active
        /// </summary>
        Active = 1,

        /// <summary>
        /// غير نشط
        /// Inactive
        /// </summary>
        Inactive = 2
    }

    /// <summary>
    /// أنواع العملات
    /// Currency Types
    /// </summary>
    public enum CurrencyType
    {
        /// <summary>
        /// ريال سعودي
        /// Saudi Riyal
        /// </summary>
        SAR = 1,

        /// <summary>
        /// دولار أمريكي
        /// US Dollar
        /// </summary>
        USD = 2,

        /// <summary>
        /// يورو
        /// Euro
        /// </summary>
        EUR = 3,

        /// <summary>
        /// جنيه إسترليني
        /// British Pound
        /// </summary>
        GBP = 4
    }

    /// <summary>
    /// نموذج بيانات دليل الحسابات
    /// Chart of Accounts Data Model
    /// </summary>
    public class ChartOfAccount
    {
        #region Properties
        /// <summary>
        /// معرف الحساب
        /// Account ID
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// رمز الحساب
        /// Account Code
        /// </summary>
        public string AccountCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم الحساب بالعربية
        /// Account Name in Arabic
        /// </summary>
        public string AccountNameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم الحساب بالإنجليزية
        /// Account Name in English
        /// </summary>
        public string AccountNameEn { get; set; } = string.Empty;

        /// <summary>
        /// معرف الحساب الأب
        /// Parent Account ID
        /// </summary>
        public int? ParentAccountId { get; set; }

        /// <summary>
        /// مستوى الحساب
        /// Account Level
        /// </summary>
        public AccountLevelType LevelType { get; set; } = AccountLevelType.Detail;

        /// <summary>
        /// طبيعة الحساب
        /// Account Nature
        /// </summary>
        public AccountNature Nature { get; set; } = AccountNature.Debit;

        /// <summary>
        /// معرف نوع الحساب
        /// Account Type ID
        /// </summary>
        public int AccountTypeId { get; set; }

        /// <summary>
        /// معرف مجموعة الحساب
        /// Account Group ID
        /// </summary>
        public int AccountGroupId { get; set; }

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// الرصيد الافتتاحي
        /// Opening Balance
        /// </summary>
        public decimal OpeningBalance { get; set; } = 0;

        /// <summary>
        /// نوع الرصيد الافتتاحي
        /// Opening Balance Type
        /// </summary>
        public AccountNature OpeningBalanceType { get; set; } = AccountNature.Debit;

        /// <summary>
        /// معرف العملة
        /// Currency ID
        /// </summary>
        public int? CurrencyId { get; set; }

        /// <summary>
        /// يسمح بالترحيل
        /// Allow Posting
        /// </summary>
        public bool AllowPosting { get; set; } = true;

        /// <summary>
        /// يسمح بالإدخال المباشر
        /// Allow Direct Entry
        /// </summary>
        public bool AllowDirectEntry { get; set; } = true;

        /// <summary>
        /// حالة النشاط
        /// Active Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// حالة الحساب
        /// Account Status
        /// </summary>
        public AccountStatus Status { get; set; } = AccountStatus.Active;

        /// <summary>
        /// نوع العملة
        /// Currency Type
        /// </summary>
        public CurrencyType Currency { get; set; } = CurrencyType.SAR;

        /// <summary>
        /// هل الحساب حساب أب (يحتوي على حسابات فرعية)
        /// Is Parent Account (contains sub accounts)
        /// </summary>
        public bool IsParentAccount { get; set; } = false;

        /// <summary>
        /// مستوى الحساب (رقمي)
        /// Account Level (numeric)
        /// </summary>
        public int AccountLevel { get; set; } = 1;

        /// <summary>
        /// طبيعة الرصيد الافتتاحي
        /// Opening Balance Nature
        /// </summary>
        public AccountNature OpeningBalanceNature { get; set; } = AccountNature.Debit;



        /// <summary>
        /// الرصيد الحالي
        /// Current Balance
        /// </summary>
        public decimal CurrentBalance { get; set; } = 0;

        /// <summary>
        /// اسم الحساب (للتوافق مع الإصدارات القديمة)
        /// Account Name (for backward compatibility)
        /// </summary>
        public string AccountName { get; set; } = string.Empty;

        /// <summary>
        /// رمز العملة
        /// Currency Code
        /// </summary>
        public string CurrencyCode { get; set; } = "SAR";

        /// <summary>
        /// اسم نوع الحساب
        /// Account Type Name
        /// </summary>
        public string AccountTypeName { get; set; } = string.Empty;

        /// <summary>
        /// اسم نوع الحساب بالعربية
        /// Account Type Name in Arabic
        /// </summary>
        public string AccountTypeNameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم المجموعة
        /// Group Name
        /// </summary>
        public string GroupName { get; set; } = string.Empty;

        /// <summary>
        /// اسم المجموعة بالعربية
        /// Group Name in Arabic
        /// </summary>
        public string GroupNameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم الحساب الأب
        /// Parent Account Name
        /// </summary>
        public string ParentAccountName { get; set; } = string.Empty;

        /// <summary>
        /// اسم الحساب الأب بالعربية
        /// Parent Account Name in Arabic
        /// </summary>
        public string ParentAccountNameAr { get; set; } = string.Empty;

        /// <summary>
        /// هل هو حساب أب
        /// Is Parent Account
        /// </summary>
        public bool IsParent { get; set; } = false;

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل
        /// Last Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم المنشئ
        /// Created By User ID
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// معرف المستخدم المعدل
        /// Modified By User ID
        /// </summary>
        public int? ModifiedBy { get; set; }
        #endregion

        #region Constructor
        /// <summary>
        /// منشئ افتراضي
        /// Default Constructor
        /// </summary>
        public ChartOfAccount()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
            AllowPosting = true;
            AllowDirectEntry = true;
            OpeningBalance = 0;
            LevelType = AccountLevelType.Detail;
            Nature = AccountNature.Debit;
            OpeningBalanceType = AccountNature.Debit;
        }
        #endregion

        #region Methods
        /// <summary>
        /// التحقق من صحة بيانات الحساب
        /// Validate account data
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(AccountCode) &&
                   !string.IsNullOrWhiteSpace(AccountNameAr) &&
                   AccountTypeId > 0 &&
                   AccountGroupId > 0;
        }

        /// <summary>
        /// إرجاع تمثيل نصي للحساب
        /// Return string representation of account
        /// </summary>
        /// <returns>النص الممثل للحساب</returns>
        public override string ToString()
        {
            return $"{AccountCode} - {AccountNameAr}";
        }

        /// <summary>
        /// مقارنة الحسابات
        /// Compare accounts
        /// </summary>
        /// <param name="obj">الكائن للمقارنة</param>
        /// <returns>true إذا كانت الحسابات متطابقة</returns>
        public override bool Equals(object obj)
        {
            ChartOfAccount other = obj as ChartOfAccount;
            if (other != null)
            {
                return AccountId == other.AccountId ||
                       (AccountId == 0 && other.AccountId == 0 &&
                        AccountCode.Equals(other.AccountCode, StringComparison.OrdinalIgnoreCase));
            }
            return false;
        }

        /// <summary>
        /// الحصول على رمز التجمع
        /// Get hash code
        /// </summary>
        /// <returns>رمز التجمع</returns>
        public override int GetHashCode()
        {
            return AccountId != 0 ? AccountId.GetHashCode() : AccountCode.GetHashCode();
        }
        #endregion
    }
}
